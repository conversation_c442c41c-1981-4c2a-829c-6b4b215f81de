/**
 * @file LightweightOrderProcessor.js - 轻量化LLM驱动的订单处理器
 * @description 重构后的轻量级订单处理系统，主要依赖LLM进行解析
 */

class LightweightOrderProcessor {
    constructor() {
        // 扩展的OTA检测规则（基于_chat.txt深度分析）
        this.otaDetectors = {
            chong_dealer: [
                // 核心标识符
                /CHONG 车头/i,
                /收单&进单/i,

                // 处理人员标记
                /\*京鱼\*/,
                /\*野马\*/,
                /\*小野马\*/,
                /\*kenny\*/,
                /\*迹象\*/,
                /\*鲸鱼\*/,

                // 订单字段标识
                /用车地点[:：]/,
                /用车时间[:：]/,
                /客人姓名[:：]/,
                /接送机类型[:：]/,
                /接送地址[:：]/,
                /结算[:：]/,
                /价格[:：].*?Rm/i,

                // 特殊服务标识
                /举牌/,
                /机场转乘/,
                /包车.*?小时/,
                /点对点/,

                // 订单状态标识
                /\*取消\*/,
                /\*Confirm\*/i,
                /\*送机取消\*/,
                /航班变动更新/,

                // 分隔符标识
                /^-{3,}$/m,
                /done分割线/i,

                // 业务流程标识
                /进单后.*?emoji/,
                /进系统后.*?send/,
                /Order ID[:：]/,
                /OTA Reference Number[:：]/
            ],

            tee: [
                /OTA[:：]\s*Tee/i,
                /𝔻𝔾 皇族.*?JB/,
                /客人[:：]Tee/,
                /Evelyn.*?012-2970850/,
                /Nicholas.*?017-5992811/,
                /Teh.*?012-4610963/,
                /Condo Menara Megah/i,
                /Somerset.*?Damansara/i
            ],

            shan: [
                /~ Shan Phang/,
                /Shan.*?添加/,
                /小野马.*?单/
            ],

            gomyhire: [
                /GoMyHire/i,
                /GMH/i,
                /Order ID[:：]\s*\d+/,
                /Customer Name[:：]/,
                /Car Type[:：]/,
                /Pickup[:：]/,
                /Destination[:：]/
            ],

            jingyu: [
                /京鱼旅行/,
                /联系人[:：]/,
                /项目[:：].*?接机/,
                /日期[:：].*?\d+月\d+日/
            ]
        };

        // Chong Dealer平台预设
        this.chongDealerPresets = {
            defaultCarTypeId: 1, // Comfort 5 Seater
            defaultBackendUserId: 2, // chong_operator
            defaultSubCategoryId: 1, // airport_pickup
            timezone: 'Asia/Kuala_Lumpur',
            currency: 'MYR'
        };

        // 智能选择规则（简化版）
        this.smartSelectionRules = {
            carTypeByPassengerCount: {
                1: 1, // Compact 5 Seater
                2: 1, 
                3: 1,
                4: 1,
                5: 2, // Comfort 7 Seater
                6: 2,
                7: 2,
                8: 3, // Premium 10 Seater
                9: 3,
                10: 3
            },
            subCategoryByServiceType: {
                '接机': 1, // airport_pickup
                '送机': 2, // airport_dropoff
                '包车': 3, // charter_service
                '点对点': 4, // point_to_point
                '机场转乘': 5 // airport_transfer
            }
        };

        this.llmProcessor = null;
        this.logger = null;
    }

    /**
     * @function initialize - 初始化处理器
     * @param {Object} dependencies - 依赖对象
     */
    initialize(dependencies) {
        this.llmProcessor = dependencies.llmProcessor;
        this.logger = dependencies.logger;
        this.logger?.info('轻量化订单处理器已初始化');
    }

    /**
     * @function processOrderText - 处理订单文本
     * @param {string} text - 订单文本
     * @returns {Object} 处理结果
     */
    async processOrderText(text) {
        const startTime = Date.now();
        
        try {
            // 1. 快速OTA类型检测（<100ms）
            const otaType = this.detectOtaType(text);
            if (!otaType) {
                return {
                    success: false,
                    error: '无法识别OTA类型',
                    processingTime: Date.now() - startTime
                };
            }

            this.logger?.info('OTA类型检测完成', { 
                otaType, 
                detectionTime: Date.now() - startTime 
            });

            // 2. LLM解析（<15秒）
            const llmResult = await this.llmProcessor.parseWithLLM(text, otaType);
            if (!llmResult.success) {
                return {
                    success: false,
                    error: `LLM解析失败: ${llmResult.error}`,
                    processingTime: Date.now() - startTime
                };
            }

            // 3. 预设插入和智能选择（<50ms）
            const enhancedOrders = this.injectPresets(llmResult.orders, otaType);

            // 4. GoMyHire格式转换（<50ms）
            const goMyHireOrders = this.convertToGoMyHireFormat(enhancedOrders);

            const totalTime = Date.now() - startTime;
            this.logger?.info('订单处理完成', {
                orderCount: goMyHireOrders.length,
                totalTime,
                otaType
            });

            return {
                success: true,
                otaType,
                orders: goMyHireOrders,
                originalOrders: llmResult.orders,
                processingTime: totalTime,
                metadata: {
                    llmProvider: llmResult.provider,
                    llmTime: llmResult.processingTime
                }
            };

        } catch (error) {
            this.logger?.error('订单处理失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }

    /**
     * @function detectOtaType - 检测OTA类型（增强版）
     * @param {string} text - 订单文本
     * @returns {string|null} OTA类型
     */
    detectOtaType(text) {
        const detectionResults = [];

        // 计算每个OTA类型的匹配分数
        for (const [otaType, patterns] of Object.entries(this.otaDetectors)) {
            let matchCount = 0;
            let totalPatterns = patterns.length;
            const matchedPatterns = [];

            for (const pattern of patterns) {
                if (pattern.test(text)) {
                    matchCount++;
                    matchedPatterns.push(pattern.source);
                }
            }

            if (matchCount > 0) {
                const confidence = matchCount / totalPatterns;
                detectionResults.push({
                    otaType,
                    matchCount,
                    totalPatterns,
                    confidence,
                    matchedPatterns
                });
            }
        }

        // 按置信度排序，返回最高分的OTA类型
        if (detectionResults.length > 0) {
            detectionResults.sort((a, b) => {
                // 优先按匹配数量排序，再按置信度排序
                if (b.matchCount !== a.matchCount) {
                    return b.matchCount - a.matchCount;
                }
                return b.confidence - a.confidence;
            });

            const bestMatch = detectionResults[0];

            this.logger?.info('OTA类型检测结果', {
                detectedType: bestMatch.otaType,
                confidence: bestMatch.confidence.toFixed(3),
                matchCount: bestMatch.matchCount,
                totalPatterns: bestMatch.totalPatterns,
                matchedPatterns: bestMatch.matchedPatterns.slice(0, 3) // 只显示前3个匹配的模式
            });

            return bestMatch.otaType;
        }

        this.logger?.warn('未检测到任何已知的OTA类型');
        return null;
    }

    /**
     * @function injectPresets - 注入平台预设和智能选择
     * @param {Array} orders - LLM解析的订单
     * @param {string} otaType - OTA类型
     * @returns {Array} 增强后的订单
     */
    injectPresets(orders, otaType) {
        return orders.map(order => {
            const enhanced = { ...order };

            if (otaType === 'chong_dealer') {
                // 注入Chong Dealer预设
                enhanced.backendUserId = this.chongDealerPresets.defaultBackendUserId;
                enhanced.currency = this.chongDealerPresets.currency;
                enhanced.timezone = this.chongDealerPresets.timezone;

                // 智能车型选择
                if (!enhanced.carTypeId && enhanced.passengerCount) {
                    enhanced.carTypeId = this.smartSelectionRules.carTypeByPassengerCount[enhanced.passengerCount] 
                        || this.chongDealerPresets.defaultCarTypeId;
                }

                // 智能子分类选择
                if (!enhanced.subCategoryId && enhanced.serviceType) {
                    enhanced.subCategoryId = this.smartSelectionRules.subCategoryByServiceType[enhanced.serviceType]
                        || this.chongDealerPresets.defaultSubCategoryId;
                }

                // 默认值填充
                enhanced.carTypeId = enhanced.carTypeId || this.chongDealerPresets.defaultCarTypeId;
                enhanced.subCategoryId = enhanced.subCategoryId || this.chongDealerPresets.defaultSubCategoryId;
            }

            return enhanced;
        });
    }

    /**
     * @function convertToGoMyHireFormat - 转换为GoMyHire API格式
     * @param {Array} orders - 增强后的订单
     * @returns {Array} GoMyHire格式订单
     */
    convertToGoMyHireFormat(orders) {
        return orders.map(order => ({
            // GoMyHire API必需字段
            customer_name: order.customerName || '',
            customer_contact: order.customerContact || order.phone || '',
            customer_email: order.customerEmail || '<EMAIL>',
            service_date: this.formatDateForGoMyHire(order.serviceDate),
            service_time: order.serviceTime || '00:00',
            service_type: order.serviceType === '接机' ? 'pickup' : 'dropoff',
            pickup_address: order.pickupAddress || '',
            dropoff_address: order.dropoffAddress || '',
            
            // 关键ID字段
            car_type_id: order.carTypeId,
            backend_user_id: order.backendUserId,
            sub_category_id: order.subCategoryId,
            
            // 可选字段
            flight_number: order.flightNumber || '',
            passenger_count: order.passengerCount || 1,
            special_requirements: order.specialServices?.join(', ') || '',
            
            // 元数据
            ota_reference: order.id || `chong_${Date.now()}`,
            original_text: order.originalText || '',
            processing_metadata: {
                llm_provider: order.llmProvider,
                confidence_score: order.confidenceScore || 0.8,
                processed_at: new Date().toISOString()
            }
        }));
    }

    /**
     * @function formatDateForGoMyHire - 格式化日期为GoMyHire格式
     * @param {string} dateStr - 原始日期字符串
     * @returns {string} DD-MM-YYYY格式日期
     */
    formatDateForGoMyHire(dateStr) {
        if (!dateStr) return '';

        try {
            // 处理中文日期格式：03月23日 -> 23-03-2024
            const chineseMatch = dateStr.match(/(\d{1,2})月(\d{1,2})日?/);
            if (chineseMatch) {
                const month = chineseMatch[1].padStart(2, '0');
                const day = chineseMatch[2].padStart(2, '0');
                const year = new Date().getFullYear(); // 默认当前年份
                return `${day}-${month}-${year}`;
            }

            // 处理简化格式：3.23 -> 23-03-2024
            const simpleMatch = dateStr.match(/(\d{1,2})\.(\d{1,2})/);
            if (simpleMatch) {
                const month = simpleMatch[1].padStart(2, '0');
                const day = simpleMatch[2].padStart(2, '0');
                const year = new Date().getFullYear();
                return `${day}-${month}-${year}`;
            }

            // 处理标准格式
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                return `${day}-${month}-${year}`;
            }

        } catch (error) {
            this.logger?.warn('日期格式转换失败', { dateStr, error: error.message });
        }

        return '';
    }

    /**
     * @function validateGoMyHireOrder - 验证GoMyHire订单格式
     * @param {Object} order - GoMyHire格式订单
     * @returns {Object} 验证结果
     */
    validateGoMyHireOrder(order) {
        const errors = [];
        const warnings = [];

        // 必填字段检查
        const requiredFields = [
            'customer_name', 'service_date', 'service_type',
            'car_type_id', 'backend_user_id', 'sub_category_id'
        ];

        for (const field of requiredFields) {
            if (!order[field]) {
                errors.push(`缺少必填字段: ${field}`);
            }
        }

        // 日期格式检查
        if (order.service_date && !/^\d{2}-\d{2}-\d{4}$/.test(order.service_date)) {
            errors.push('日期格式不正确，应为DD-MM-YYYY');
        }

        // ID字段检查
        if (order.car_type_id && !Number.isInteger(order.car_type_id)) {
            errors.push('car_type_id必须为整数');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LightweightOrderProcessor;
} else if (typeof window !== 'undefined') {
    window.LightweightOrderProcessor = LightweightOrderProcessor;
}
